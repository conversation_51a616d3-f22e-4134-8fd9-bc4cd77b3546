<?php
require_once '../config/config.php';

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $type = $input['type'] ?? '';
    $value = sanitize_input($input['value'] ?? '');
    
    if (empty($type) || empty($value)) {
        throw new Exception('Type and value are required');
    }
    
    $available = false;
    
    switch ($type) {
        case 'username':
            $query = "SELECT id FROM users WHERE username = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$value]);
            $available = !$stmt->fetch();
            break;
            
        case 'email':
            $query = "SELECT id FROM users WHERE email = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$value]);
            $available = !$stmt->fetch();
            break;
            
        default:
            throw new Exception('Invalid type');
    }
    
    echo json_encode([
        'success' => true,
        'available' => $available
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
