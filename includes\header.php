<?php
// Get cart count
$cart_count = 0;
if (isset($_SESSION['user_id'])) {
    $query = "SELECT SUM(quantity) as count FROM cart WHERE user_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$_SESSION['user_id']]);
    $result = $stmt->fetch();
    $cart_count = $result['count'] ?? 0;
} elseif (isset($_SESSION['session_id'])) {
    $query = "SELECT SUM(quantity) as count FROM cart WHERE session_id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$_SESSION['session_id']]);
    $result = $stmt->fetch();
    $cart_count = $result['count'] ?? 0;
}

// Get categories for navigation
$query = "SELECT * FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY sort_order, name";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();
?>

<header class="bg-white shadow-md sticky top-0 z-40">
    <!-- Top bar -->
    <div class="bg-gray-800 text-white text-sm">
        <div class="container mx-auto px-4 py-2 flex justify-between items-center">
            <div>
                <span><i class="fas fa-phone mr-2"></i>+****************</span>
                <span class="ml-4"><i class="fas fa-envelope mr-2"></i><EMAIL></span>
            </div>
            <div class="flex items-center space-x-4">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <a href="?page=profile" class="hover:text-gray-300">
                        <i class="fas fa-user mr-1"></i>My Account
                    </a>
                    <a href="api/auth.php?action=logout" class="hover:text-gray-300">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </a>
                <?php else: ?>
                    <a href="?page=login" class="hover:text-gray-300">
                        <i class="fas fa-sign-in-alt mr-1"></i>Login
                    </a>
                    <a href="?page=register" class="hover:text-gray-300">
                        <i class="fas fa-user-plus mr-1"></i>Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Main header -->
    <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="index.php" class="text-2xl font-bold text-primary">
                    <i class="fas fa-store mr-2"></i>
                    <?php echo htmlspecialchars($site_settings['site_name']); ?>
                </a>
            </div>

            <!-- Search bar -->
            <div class="flex-1 max-w-lg mx-8">
                <form action="?page=products" method="GET" class="relative">
                    <input type="hidden" name="page" value="products">
                    <input type="text" name="search" placeholder="Search products..." 
                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                           class="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    <button type="submit" class="absolute right-2 top-1 bg-primary text-white px-4 py-1 rounded hover:bg-primary-dark">
                        Search
                    </button>
                </form>
            </div>

            <!-- Cart and actions -->
            <div class="flex items-center space-x-4">
                <?php if ($site_settings['features']['wishlist']): ?>
                <a href="?page=wishlist" class="text-gray-600 hover:text-primary relative">
                    <i class="fas fa-heart text-xl"></i>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                </a>
                <?php endif; ?>

                <a href="?page=cart" class="text-gray-600 hover:text-primary relative">
                    <i class="fas fa-shopping-cart text-xl"></i>
                    <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count"><?php echo $cart_count; ?></span>
                </a>

                <!-- Mobile menu button -->
                <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-primary">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-gray-100 border-t">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <!-- Categories dropdown -->
                <div class="relative group">
                    <button class="flex items-center px-4 py-3 text-gray-700 hover:text-primary">
                        <i class="fas fa-bars mr-2"></i>
                        All Categories
                        <i class="fas fa-chevron-down ml-2"></i>
                    </button>
                    <div class="absolute left-0 top-full bg-white shadow-lg rounded-md w-64 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <?php foreach ($categories as $category): ?>
                        <a href="?page=products&category=<?php echo $category['slug']; ?>" 
                           class="block px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-primary">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Main navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.php" class="text-gray-700 hover:text-primary py-3 <?php echo ($page === 'home') ? 'text-primary border-b-2 border-primary' : ''; ?>">
                        Home
                    </a>
                    <a href="?page=products" class="text-gray-700 hover:text-primary py-3 <?php echo ($page === 'products') ? 'text-primary border-b-2 border-primary' : ''; ?>">
                        Products
                    </a>
                    <a href="?page=contact" class="text-gray-700 hover:text-primary py-3 <?php echo ($page === 'contact') ? 'text-primary border-b-2 border-primary' : ''; ?>">
                        Contact
                    </a>
                </div>

                <!-- Special offers -->
                <div class="hidden md:block">
                    <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm">
                        <i class="fas fa-fire mr-1"></i>Hot Deals
                    </span>
                </div>
            </div>
        </div>

        <!-- Mobile navigation -->
        <div id="mobile-menu" class="md:hidden bg-white border-t hidden">
            <div class="px-4 py-2 space-y-2">
                <a href="index.php" class="block py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="?page=products" class="block py-2 text-gray-700 hover:text-primary">Products</a>
                <a href="?page=contact" class="block py-2 text-gray-700 hover:text-primary">Contact</a>
                <div class="border-t pt-2">
                    <p class="text-sm font-medium text-gray-500 mb-2">Categories</p>
                    <?php foreach ($categories as $category): ?>
                    <a href="?page=products&category=<?php echo $category['slug']; ?>" 
                       class="block py-1 text-gray-600 hover:text-primary ml-4">
                        <?php echo htmlspecialchars($category['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </nav>
</header>

<script>
// Mobile menu toggle
document.getElementById('mobile-menu-btn').addEventListener('click', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('hidden');
});
</script>
