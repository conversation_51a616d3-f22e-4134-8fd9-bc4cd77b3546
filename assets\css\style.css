/* Custom CSS for E-Commerce Site */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Product card hover effects */
.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Image zoom effect */
.image-zoom {
    overflow: hidden;
}

.image-zoom img {
    transition: transform 0.3s ease;
}

.image-zoom:hover img {
    transform: scale(1.1);
}

/* Button animations */
.btn-animate {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-animate:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

/* Notification styles */
.notification {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.error {
    border-color: #ef4444;
}

.form-control.error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Badge styles */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.badge-success {
    background-color: #10b981;
    color: white;
}

.badge-warning {
    background-color: #f59e0b;
    color: white;
}

.badge-danger {
    background-color: #ef4444;
    color: white;
}

.badge-info {
    background-color: #3b82f6;
    color: white;
}

/* Star rating */
.star-rating {
    display: inline-flex;
    align-items: center;
}

.star {
    color: #d1d5db;
    cursor: pointer;
    transition: color 0.2s;
}

.star.filled {
    color: #fbbf24;
}

.star:hover,
.star.hover {
    color: #fbbf24;
}

/* Price styles */
.price {
    font-weight: 600;
    color: #059669;
}

.price-old {
    text-decoration: line-through;
    color: #6b7280;
    font-weight: normal;
}

.price-discount {
    background-color: #ef4444;
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Cart dropdown */
.cart-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 50;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.cart-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #6b7280;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: #d1d5db;
}

.breadcrumb-item.active {
    color: #374151;
    font-weight: 500;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 640px) {
    .grid-responsive {
        grid-template-columns: 1fr;
    }
    
    .text-responsive {
        font-size: 0.875rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .bg-white {
        background-color: #374151;
    }
    
    .dark-mode .text-gray-900 {
        color: #f9fafb;
    }
    
    .dark-mode .border-gray-300 {
        border-color: #4b5563;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}
