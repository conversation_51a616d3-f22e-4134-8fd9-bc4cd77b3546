<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'list':
        handleListProducts();
        break;
    case 'get':
        handleGetProduct();
        break;
    case 'search':
        handleSearchProducts();
        break;
    case 'quick_view':
        handleQuickView();
        break;
    case 'featured':
        handleFeaturedProducts();
        break;
    case 'related':
        handleRelatedProducts();
        break;
    case 'by_category':
        handleProductsByCategory();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

function handleListProducts() {
    global $db;
    
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(50, intval($_GET['limit'] ?? PRODUCTS_PER_PAGE)));
        $offset = ($page - 1) * $limit;
        
        $category = sanitize_input($_GET['category'] ?? '');
        $search = sanitize_input($_GET['search'] ?? '');
        $sort = sanitize_input($_GET['sort'] ?? 'name');
        $order = sanitize_input($_GET['order'] ?? 'ASC');
        $min_price = floatval($_GET['min_price'] ?? 0);
        $max_price = floatval($_GET['max_price'] ?? 0);
        
        // Build WHERE clause
        $where_conditions = ["p.status = 'active'"];
        $params = [];
        
        if ($category) {
            $where_conditions[] = "c.slug = ?";
            $params[] = $category;
        }
        
        if ($search) {
            $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
            $search_term = "%$search%";
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        if ($min_price > 0) {
            $where_conditions[] = "p.price >= ?";
            $params[] = $min_price;
        }
        
        if ($max_price > 0) {
            $where_conditions[] = "p.price <= ?";
            $params[] = $max_price;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Validate sort column
        $allowed_sorts = ['name', 'price', 'created_at', 'featured'];
        if (!in_array($sort, $allowed_sorts)) {
            $sort = 'name';
        }
        
        // Validate order
        $order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';
        
        // Get total count
        $count_query = "SELECT COUNT(*) as total 
                       FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       WHERE $where_clause";
        $stmt = $db->prepare($count_query);
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get products
        $query = "SELECT p.*, c.name as category_name, c.slug as category_slug,
                         (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE $where_clause 
                  ORDER BY p.$sort $order 
                  LIMIT $limit OFFSET $offset";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        // Format products
        foreach ($products as &$product) {
            $product['price'] = floatval($product['price']);
            $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
            $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
            $product['review_count'] = intval($product['review_count']);
            $product['primary_image'] = $product['primary_image'] ?: 'assets/images/no-image.jpg';
            $product['discount_percentage'] = 0;
            
            if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
                $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
            }
        }
        
        echo json_encode([
            'success' => true,
            'products' => $products,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_items' => $total,
                'items_per_page' => $limit
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleGetProduct() {
    global $db;
    
    try {
        $id = intval($_GET['id'] ?? 0);
        $slug = sanitize_input($_GET['slug'] ?? '');
        
        if (!$id && !$slug) {
            throw new Exception('Product ID or slug is required');
        }
        
        // Build query
        $where_clause = $id ? "p.id = ?" : "p.slug = ?";
        $param = $id ?: $slug;
        
        $query = "SELECT p.*, c.name as category_name, c.slug as category_slug,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE $where_clause AND p.status = 'active'";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$param]);
        $product = $stmt->fetch();
        
        if (!$product) {
            throw new Exception('Product not found');
        }
        
        // Get product images
        $query = "SELECT * FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, sort_order";
        $stmt = $db->prepare($query);
        $stmt->execute([$product['id']]);
        $product['images'] = $stmt->fetchAll();
        
        // Get product attributes
        $query = "SELECT * FROM product_attributes WHERE product_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$product['id']]);
        $product['attributes'] = $stmt->fetchAll();
        
        // Get reviews
        $query = "SELECT r.*, u.first_name, u.last_name 
                  FROM product_reviews r 
                  LEFT JOIN users u ON r.user_id = u.id 
                  WHERE r.product_id = ? AND r.is_approved = 1 
                  ORDER BY r.created_at DESC 
                  LIMIT 10";
        $stmt = $db->prepare($query);
        $stmt->execute([$product['id']]);
        $product['reviews'] = $stmt->fetchAll();
        
        // Format data
        $product['price'] = floatval($product['price']);
        $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
        $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
        $product['review_count'] = intval($product['review_count']);
        $product['discount_percentage'] = 0;
        
        if ($product['sale_price'] && $product['sale_price'] < $product['price']) {
            $product['discount_percentage'] = round((($product['price'] - $product['sale_price']) / $product['price']) * 100);
        }
        
        echo json_encode([
            'success' => true,
            'product' => $product
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleSearchProducts() {
    global $db;
    
    try {
        $query = sanitize_input($_GET['q'] ?? '');
        $limit = min(10, intval($_GET['limit'] ?? 10));
        
        if (strlen($query) < 2) {
            echo json_encode(['success' => true, 'suggestions' => []]);
            return;
        }
        
        $search_term = "%$query%";
        
        $sql = "SELECT p.id, p.name, p.slug, p.price, p.sale_price,
                       (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image
                FROM products p 
                WHERE p.status = 'active' AND (p.name LIKE ? OR p.description LIKE ?) 
                ORDER BY p.name 
                LIMIT ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$search_term, $search_term, $limit]);
        $suggestions = $stmt->fetchAll();
        
        foreach ($suggestions as &$suggestion) {
            $suggestion['price'] = floatval($suggestion['price']);
            $suggestion['sale_price'] = $suggestion['sale_price'] ? floatval($suggestion['sale_price']) : null;
            $suggestion['image'] = $suggestion['image'] ?: 'assets/images/no-image.jpg';
        }
        
        echo json_encode([
            'success' => true,
            'suggestions' => $suggestions
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleQuickView() {
    global $db;
    
    try {
        $id = intval($_GET['id'] ?? 0);
        
        if (!$id) {
            throw new Exception('Product ID is required');
        }
        
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as image,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.id = ? AND p.status = 'active'";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$id]);
        $product = $stmt->fetch();
        
        if (!$product) {
            throw new Exception('Product not found');
        }
        
        $product['price'] = floatval($product['price']);
        $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
        $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
        $product['review_count'] = intval($product['review_count']);
        $product['image'] = $product['image'] ?: 'assets/images/no-image.jpg';
        
        echo json_encode([
            'success' => true,
            'product' => $product
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleFeaturedProducts() {
    global $db;
    
    try {
        $limit = min(20, intval($_GET['limit'] ?? 8));
        
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.status = 'active' AND p.featured = 1 
                  ORDER BY p.created_at DESC 
                  LIMIT ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$limit]);
        $products = $stmt->fetchAll();
        
        foreach ($products as &$product) {
            $product['price'] = floatval($product['price']);
            $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
            $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
            $product['review_count'] = intval($product['review_count']);
            $product['primary_image'] = $product['primary_image'] ?: 'assets/images/no-image.jpg';
        }
        
        echo json_encode([
            'success' => true,
            'products' => $products
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleRelatedProducts() {
    global $db;
    
    try {
        $product_id = intval($_GET['product_id'] ?? 0);
        $limit = min(10, intval($_GET['limit'] ?? 4));
        
        if (!$product_id) {
            throw new Exception('Product ID is required');
        }
        
        // Get the category of the current product
        $query = "SELECT category_id FROM products WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$product_id]);
        $current_product = $stmt->fetch();
        
        if (!$current_product) {
            throw new Exception('Product not found');
        }
        
        // Get related products from the same category
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.status = 'active' AND p.category_id = ? AND p.id != ? 
                  ORDER BY RAND() 
                  LIMIT ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$current_product['category_id'], $product_id, $limit]);
        $products = $stmt->fetchAll();
        
        foreach ($products as &$product) {
            $product['price'] = floatval($product['price']);
            $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
            $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
            $product['review_count'] = intval($product['review_count']);
            $product['primary_image'] = $product['primary_image'] ?: 'assets/images/no-image.jpg';
        }
        
        echo json_encode([
            'success' => true,
            'products' => $products
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleProductsByCategory() {
    global $db;
    
    try {
        $category_slug = sanitize_input($_GET['category'] ?? '');
        $limit = min(20, intval($_GET['limit'] ?? 12));
        
        if (!$category_slug) {
            throw new Exception('Category is required');
        }
        
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                         (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                         (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.status = 'active' AND c.slug = ? 
                  ORDER BY p.featured DESC, p.created_at DESC 
                  LIMIT ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$category_slug, $limit]);
        $products = $stmt->fetchAll();
        
        foreach ($products as &$product) {
            $product['price'] = floatval($product['price']);
            $product['sale_price'] = $product['sale_price'] ? floatval($product['sale_price']) : null;
            $product['avg_rating'] = $product['avg_rating'] ? round(floatval($product['avg_rating']), 1) : 0;
            $product['review_count'] = intval($product['review_count']);
            $product['primary_image'] = $product['primary_image'] ?: 'assets/images/no-image.jpg';
        }
        
        echo json_encode([
            'success' => true,
            'products' => $products
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
?>
