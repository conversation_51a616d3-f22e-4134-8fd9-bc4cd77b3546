<?php
require_once '../config/config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$input = json_decode(file_get_contents('php://input'), true);
$action = $action ?: ($input['action'] ?? '');

switch ($action) {
    case 'update_profile':
        handleUpdateProfile();
        break;
    case 'change_password':
        handleChangePassword();
        break;
    case 'add_address':
        handleAddAddress();
        break;
    case 'update_address':
        handleUpdateAddress();
        break;
    case 'delete_address':
        handleDeleteAddress();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

function handleUpdateProfile() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $first_name = sanitize_input($input['first_name'] ?? '');
        $last_name = sanitize_input($input['last_name'] ?? '');
        $email = sanitize_input($input['email'] ?? '');
        $phone = sanitize_input($input['phone'] ?? '');
        
        // Validation
        if (empty($first_name) || empty($last_name) || empty($email)) {
            throw new Exception('First name, last name, and email are required');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }
        
        // Check if email is already taken by another user
        $query = "SELECT id FROM users WHERE email = ? AND id != ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$email, $_SESSION['user_id']]);
        
        if ($stmt->fetch()) {
            throw new Exception('Email is already taken by another user');
        }
        
        // Update user profile
        $query = "UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$first_name, $last_name, $email, $phone, $_SESSION['user_id']]);
        
        // Update session data
        $_SESSION['user_name'] = $first_name . ' ' . $last_name;
        
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleChangePassword() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $current_password = $input['current_password'] ?? '';
        $new_password = $input['new_password'] ?? '';
        
        if (empty($current_password) || empty($new_password)) {
            throw new Exception('Current password and new password are required');
        }
        
        if (strlen($new_password) < 6) {
            throw new Exception('New password must be at least 6 characters long');
        }
        
        // Get current password hash
        $query = "SELECT password FROM users WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($current_password, $user['password'])) {
            throw new Exception('Current password is incorrect');
        }
        
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $query = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$hashed_password, $_SESSION['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleAddAddress() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $type = sanitize_input($input['type'] ?? '');
        $first_name = sanitize_input($input['first_name'] ?? '');
        $last_name = sanitize_input($input['last_name'] ?? '');
        $company = sanitize_input($input['company'] ?? '');
        $address_line_1 = sanitize_input($input['address_line_1'] ?? '');
        $address_line_2 = sanitize_input($input['address_line_2'] ?? '');
        $city = sanitize_input($input['city'] ?? '');
        $state = sanitize_input($input['state'] ?? '');
        $postal_code = sanitize_input($input['postal_code'] ?? '');
        $country = sanitize_input($input['country'] ?? '');
        $phone = sanitize_input($input['phone'] ?? '');
        $is_default = $input['is_default'] ?? false;
        
        // Validation
        if (empty($type) || empty($first_name) || empty($last_name) || empty($address_line_1) || 
            empty($city) || empty($state) || empty($postal_code) || empty($country)) {
            throw new Exception('All required fields must be filled');
        }
        
        if (!in_array($type, ['billing', 'shipping'])) {
            throw new Exception('Invalid address type');
        }
        
        // If this is set as default, remove default from other addresses of same type
        if ($is_default) {
            $query = "UPDATE user_addresses SET is_default = 0 WHERE user_id = ? AND type = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$_SESSION['user_id'], $type]);
        }
        
        // Insert new address
        $query = "INSERT INTO user_addresses (user_id, type, first_name, last_name, company, address_line_1, address_line_2, city, state, postal_code, country, phone, is_default) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->execute([
            $_SESSION['user_id'], $type, $first_name, $last_name, $company,
            $address_line_1, $address_line_2, $city, $state, $postal_code, $country, $phone, $is_default
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Address added successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleUpdateAddress() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $address_id = $input['address_id'] ?? 0;
        $type = sanitize_input($input['type'] ?? '');
        $first_name = sanitize_input($input['first_name'] ?? '');
        $last_name = sanitize_input($input['last_name'] ?? '');
        $company = sanitize_input($input['company'] ?? '');
        $address_line_1 = sanitize_input($input['address_line_1'] ?? '');
        $address_line_2 = sanitize_input($input['address_line_2'] ?? '');
        $city = sanitize_input($input['city'] ?? '');
        $state = sanitize_input($input['state'] ?? '');
        $postal_code = sanitize_input($input['postal_code'] ?? '');
        $country = sanitize_input($input['country'] ?? '');
        $phone = sanitize_input($input['phone'] ?? '');
        $is_default = $input['is_default'] ?? false;
        
        // Validation
        if (empty($address_id) || empty($type) || empty($first_name) || empty($last_name) || 
            empty($address_line_1) || empty($city) || empty($state) || empty($postal_code) || empty($country)) {
            throw new Exception('All required fields must be filled');
        }
        
        // Verify address belongs to user
        $query = "SELECT id FROM user_addresses WHERE id = ? AND user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$address_id, $_SESSION['user_id']]);
        
        if (!$stmt->fetch()) {
            throw new Exception('Address not found');
        }
        
        // If this is set as default, remove default from other addresses of same type
        if ($is_default) {
            $query = "UPDATE user_addresses SET is_default = 0 WHERE user_id = ? AND type = ? AND id != ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$_SESSION['user_id'], $type, $address_id]);
        }
        
        // Update address
        $query = "UPDATE user_addresses SET type = ?, first_name = ?, last_name = ?, company = ?, address_line_1 = ?, address_line_2 = ?, city = ?, state = ?, postal_code = ?, country = ?, phone = ?, is_default = ? WHERE id = ? AND user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([
            $type, $first_name, $last_name, $company, $address_line_1, $address_line_2,
            $city, $state, $postal_code, $country, $phone, $is_default, $address_id, $_SESSION['user_id']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Address updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleDeleteAddress() {
    global $db;
    
    try {
        $address_id = $_GET['id'] ?? 0;
        
        if (empty($address_id)) {
            throw new Exception('Address ID is required');
        }
        
        // Verify address belongs to user
        $query = "SELECT id FROM user_addresses WHERE id = ? AND user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$address_id, $_SESSION['user_id']]);
        
        if (!$stmt->fetch()) {
            throw new Exception('Address not found');
        }
        
        // Delete address
        $query = "DELETE FROM user_addresses WHERE id = ? AND user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$address_id, $_SESSION['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Address deleted successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
?>
