<?php
// Get featured products
$query = "SELECT p.*, c.name as category_name,
                 (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                 (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                 (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          WHERE p.status = 'active' AND p.featured = 1 
          ORDER BY p.created_at DESC 
          LIMIT 8";
$stmt = $db->prepare($query);
$stmt->execute();
$featured_products = $stmt->fetchAll();

// Get categories for showcase
$query = "SELECT * FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY sort_order, name LIMIT 6";
$stmt = $db->prepare($query);
$stmt->execute();
$showcase_categories = $stmt->fetchAll();
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary to-blue-600 text-white">
    <div class="container mx-auto px-4 py-16">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    Welcome to <?php echo htmlspecialchars($site_settings['site_name']); ?>
                </h1>
                <p class="text-xl mb-8 text-blue-100">
                    Discover amazing products at unbeatable prices. Quality guaranteed, fast shipping, and excellent customer service.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="?page=products" class="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors btn-animate">
                        Shop Now
                    </a>
                    <a href="#featured" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                        View Featured
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="relative">
                    <div class="w-80 h-80 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto">
                        <i class="fas fa-shopping-bag text-8xl text-white"></i>
                    </div>
                    <div class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
                        Hot Deals!
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shipping-fast text-2xl text-white"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Fast Shipping</h3>
                <p class="text-gray-600">Free shipping on orders over $50. Express delivery available.</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-2xl text-white"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Secure Payment</h3>
                <p class="text-gray-600">Your payment information is safe and secure with us.</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-undo text-2xl text-white"></i>
                </div>
                <h3 class="text-xl font-semibold mb-2">Easy Returns</h3>
                <p class="text-gray-600">30-day return policy. No questions asked.</p>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">Shop by Category</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Explore our wide range of categories and find exactly what you're looking for.
            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <?php foreach ($showcase_categories as $category): ?>
            <a href="?page=products&category=<?php echo $category['slug']; ?>" 
               class="group text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary group-hover:text-white transition-colors">
                    <i class="fas fa-cube text-2xl"></i>
                </div>
                <h3 class="font-semibold text-gray-800 group-hover:text-primary transition-colors">
                    <?php echo htmlspecialchars($category['name']); ?>
                </h3>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section id="featured" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold mb-4">Featured Products</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Check out our handpicked selection of the best products.
            </p>
        </div>
        
        <?php if (empty($featured_products)): ?>
        <div class="text-center py-12">
            <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600">No featured products available at the moment.</p>
        </div>
        <?php else: ?>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <?php foreach ($featured_products as $product): ?>
            <div class="product-card bg-white rounded-lg shadow-md overflow-hidden">
                <div class="relative image-zoom">
                    <img src="<?php echo $product['primary_image'] ?: 'assets/images/no-image.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                         class="w-full h-48 object-cover">
                    
                    <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                    <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
                        <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>% OFF
                    </span>
                    <?php endif; ?>
                    
                    <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <?php if ($site_settings['features']['wishlist']): ?>
                        <button class="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 mb-2" 
                                onclick="addToWishlist(<?php echo $product['id']; ?>)"
                                data-tooltip="Add to Wishlist">
                            <i class="fas fa-heart text-gray-600"></i>
                        </button>
                        <?php endif; ?>
                        <button class="bg-white p-2 rounded-full shadow-md hover:bg-gray-100" 
                                onclick="openQuickView(event)" 
                                data-product-id="<?php echo $product['id']; ?>"
                                data-tooltip="Quick View">
                            <i class="fas fa-eye text-gray-600"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-4">
                    <div class="text-sm text-gray-500 mb-1"><?php echo htmlspecialchars($product['category_name']); ?></div>
                    <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">
                        <a href="?page=product&slug=<?php echo $product['slug']; ?>" class="hover:text-primary">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </a>
                    </h3>
                    
                    <?php if ($product['avg_rating'] > 0): ?>
                    <div class="flex items-center mb-2">
                        <div class="star-rating">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star star <?php echo $i <= $product['avg_rating'] ? 'filled' : ''; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="text-sm text-gray-500 ml-2">(<?php echo $product['review_count']; ?>)</span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="flex items-center justify-between">
                        <div class="price-container">
                            <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                            <span class="price text-lg">$<?php echo number_format($product['sale_price'], 2); ?></span>
                            <span class="price-old text-sm ml-2">$<?php echo number_format($product['price'], 2); ?></span>
                            <?php else: ?>
                            <span class="price text-lg">$<?php echo number_format($product['price'], 2); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <button onclick="addToCart(<?php echo $product['id']; ?>)" 
                                class="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition-colors btn-animate">
                            <i class="fas fa-cart-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-12">
            <a href="?page=products" class="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors btn-animate">
                View All Products
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Section -->
<?php if ($site_settings['features']['newsletter']): ?>
<section class="py-16 bg-primary text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-4">Stay Updated</h2>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and be the first to know about new products, special offers, and exclusive deals.
        </p>
        
        <form id="home-newsletter-form" class="max-w-md mx-auto flex">
            <input type="email" id="home-newsletter-email" placeholder="Enter your email" required
                   class="flex-1 px-4 py-3 rounded-l-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300">
            <button type="submit" 
                    class="bg-white text-primary px-6 py-3 rounded-r-lg font-semibold hover:bg-gray-100 transition-colors">
                Subscribe
            </button>
        </form>
    </div>
</section>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter form submission
    <?php if ($site_settings['features']['newsletter']): ?>
    document.getElementById('home-newsletter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('home-newsletter-email').value;
        
        fetch('api/newsletter.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?php echo generate_csrf_token(); ?>'
            },
            body: JSON.stringify({
                action: 'subscribe',
                email: email
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Successfully subscribed to newsletter!', 'success');
                document.getElementById('home-newsletter-email').value = '';
            } else {
                showNotification(data.message || 'Error subscribing to newsletter', 'error');
            }
        })
        .catch(error => {
            showNotification('Error subscribing to newsletter', 'error');
        });
    });
    <?php endif; ?>
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Wishlist functionality
function addToWishlist(productId) {
    <?php if (isset($_SESSION['user_id'])): ?>
    fetch('api/wishlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '<?php echo generate_csrf_token(); ?>'
        },
        body: JSON.stringify({
            action: 'add',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product added to wishlist!', 'success');
        } else {
            showNotification(data.message || 'Error adding to wishlist', 'error');
        }
    })
    .catch(error => {
        showNotification('Error adding to wishlist', 'error');
    });
    <?php else: ?>
    showNotification('Please login to add items to wishlist', 'error');
    setTimeout(() => {
        window.location.href = '?page=login';
    }, 2000);
    <?php endif; ?>
}
</script>
