<footer class="bg-gray-800 text-white">
    <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div>
                <h3 class="text-xl font-bold mb-4">
                    <i class="fas fa-store mr-2"></i>
                    <?php echo htmlspecialchars($site_settings['site_name']); ?>
                </h3>
                <p class="text-gray-300 mb-4">
                    Your trusted online store for quality products at competitive prices. 
                    We're committed to providing excellent customer service and fast delivery.
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-300 hover:text-white">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.php" class="text-gray-300 hover:text-white">Home</a></li>
                    <li><a href="?page=products" class="text-gray-300 hover:text-white">Products</a></li>
                    <li><a href="?page=contact" class="text-gray-300 hover:text-white">Contact Us</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">About Us</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">Privacy Policy</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">Terms of Service</a></li>
                </ul>
            </div>

            <!-- Categories -->
            <div>
                <h4 class="text-lg font-semibold mb-4">Categories</h4>
                <ul class="space-y-2">
                    <?php
                    $query = "SELECT * FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY name LIMIT 6";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    $footer_categories = $stmt->fetchAll();
                    
                    foreach ($footer_categories as $category):
                    ?>
                    <li>
                        <a href="?page=products&category=<?php echo $category['slug']; ?>" 
                           class="text-gray-300 hover:text-white">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <!-- Contact Info & Newsletter -->
            <div>
                <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                <div class="space-y-3 text-gray-300">
                    <div class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-3"></i>
                        <span>123 Business Street, City, State 12345</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-phone mr-3"></i>
                        <span>+****************</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-envelope mr-3"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock mr-3"></i>
                        <span>Mon - Fri: 9:00 AM - 6:00 PM</span>
                    </div>
                </div>

                <?php if ($site_settings['features']['newsletter']): ?>
                <!-- Newsletter Signup -->
                <div class="mt-6">
                    <h5 class="font-semibold mb-3">Newsletter</h5>
                    <form id="newsletter-form" class="flex">
                        <input type="email" id="newsletter-email" placeholder="Your email" required
                               class="flex-1 px-3 py-2 bg-gray-700 text-white border border-gray-600 rounded-l focus:outline-none focus:ring-2 focus:ring-primary">
                        <button type="submit" 
                                class="bg-primary hover:bg-primary-dark px-4 py-2 rounded-r transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bottom Bar -->
    <div class="border-t border-gray-700">
        <div class="container mx-auto px-4 py-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-gray-300 text-sm">
                    © <?php echo date('Y'); ?> <?php echo htmlspecialchars($site_settings['site_name']); ?>. All rights reserved.
                </div>
                <div class="flex items-center space-x-4 mt-4 md:mt-0">
                    <span class="text-gray-300 text-sm">We Accept:</span>
                    <div class="flex space-x-2">
                        <i class="fab fa-cc-visa text-2xl text-blue-600"></i>
                        <i class="fab fa-cc-mastercard text-2xl text-red-600"></i>
                        <i class="fab fa-cc-paypal text-2xl text-blue-500"></i>
                        <i class="fab fa-cc-stripe text-2xl text-purple-600"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<?php if ($site_settings['features']['newsletter']): ?>
<script>
// Newsletter subscription
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('newsletter-email').value;
    
    fetch('api/newsletter.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '<?php echo generate_csrf_token(); ?>'
        },
        body: JSON.stringify({
            action: 'subscribe',
            email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Successfully subscribed to newsletter!', 'success');
            document.getElementById('newsletter-email').value = '';
        } else {
            showNotification(data.message || 'Error subscribing to newsletter', 'error');
        }
    })
    .catch(error => {
        showNotification('Error subscribing to newsletter', 'error');
    });
});
</script>
<?php endif; ?>

<!-- Back to top button -->
<button id="back-to-top" 
        class="fixed bottom-4 right-4 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-all duration-300 opacity-0 invisible">
    <i class="fas fa-arrow-up"></i>
</button>

<script>
// Back to top functionality
window.addEventListener('scroll', function() {
    const backToTop = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.classList.remove('opacity-0', 'invisible');
    } else {
        backToTop.classList.add('opacity-0', 'invisible');
    }
});

document.getElementById('back-to-top').addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});
</script>
