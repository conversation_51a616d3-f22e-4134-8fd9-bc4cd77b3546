<?php
require_once '../config/config.php';

header('Content-Type: application/json');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'register':
        handleRegister();
        break;
    case 'login':
        handleLogin();
        break;
    case 'logout':
        handleLogout();
        break;
    case 'forgot_password':
        handleForgotPassword();
        break;
    case 'reset_password':
        handleResetPassword();
        break;
    case 'verify_email':
        handleVerifyEmail();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

function handleRegister() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        // Validate input
        $username = sanitize_input($input['username'] ?? '');
        $email = sanitize_input($input['email'] ?? '');
        $password = $input['password'] ?? '';
        $confirm_password = $input['confirm_password'] ?? '';
        $first_name = sanitize_input($input['first_name'] ?? '');
        $last_name = sanitize_input($input['last_name'] ?? '');
        $phone = sanitize_input($input['phone'] ?? '');
        
        // Validation
        if (empty($username) || empty($email) || empty($password) || empty($first_name) || empty($last_name)) {
            throw new Exception('All required fields must be filled');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Password must be at least 6 characters long');
        }
        
        if ($password !== $confirm_password) {
            throw new Exception('Passwords do not match');
        }
        
        // Check if username or email already exists
        $query = "SELECT id FROM users WHERE username = ? OR email = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            throw new Exception('Username or email already exists');
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Generate email verification token
        $verification_token = bin2hex(random_bytes(32));
        
        // Insert user
        $query = "INSERT INTO users (username, email, password, first_name, last_name, phone, email_verified) 
                 VALUES (?, ?, ?, ?, ?, ?, 0)";
        $stmt = $db->prepare($query);
        $stmt->execute([$username, $email, $hashed_password, $first_name, $last_name, $phone]);
        
        $user_id = $db->lastInsertId();
        
        // Send verification email (implement email sending)
        sendVerificationEmail($email, $verification_token);
        
        echo json_encode([
            'success' => true,
            'message' => 'Registration successful! Please check your email to verify your account.'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleLogin() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate CSRF token
        if (!verify_csrf_token($input['csrf_token'] ?? '')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $username_or_email = sanitize_input($input['username'] ?? '');
        $password = $input['password'] ?? '';
        $remember_me = $input['remember_me'] ?? false;
        
        if (empty($username_or_email) || empty($password)) {
            throw new Exception('Username/email and password are required');
        }
        
        // Find user
        $query = "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->execute([$username_or_email, $username_or_email]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($password, $user['password'])) {
            throw new Exception('Invalid credentials');
        }
        
        // Check if email is verified
        if (!$user['email_verified']) {
            throw new Exception('Please verify your email before logging in');
        }
        
        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['is_admin'] = $user['is_admin'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        
        // Set remember me cookie if requested
        if ($remember_me) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
            
            // Store token in database (implement remember_tokens table if needed)
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'redirect' => $user['is_admin'] ? 'admin/' : '?page=profile'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleLogout() {
    // Clear session
    session_destroy();
    
    // Clear remember me cookie
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Redirect to home page
    header('Location: ../index.php');
    exit;
}

function handleForgotPassword() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $email = sanitize_input($input['email'] ?? '');
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Valid email is required');
        }
        
        // Check if user exists
        $query = "SELECT id, first_name FROM users WHERE email = ? AND is_active = 1";
        $stmt = $db->prepare($query);
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Don't reveal if email exists or not
            echo json_encode([
                'success' => true,
                'message' => 'If the email exists, a reset link has been sent.'
            ]);
            return;
        }
        
        // Generate reset token
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        
        // Delete old tokens
        $query = "DELETE FROM password_resets WHERE email = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$email]);
        
        // Insert new token
        $query = "INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->execute([$email, $token, $expires_at]);
        
        // Send reset email
        sendPasswordResetEmail($email, $token, $user['first_name']);
        
        echo json_encode([
            'success' => true,
            'message' => 'Password reset link has been sent to your email.'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function handleResetPassword() {
    global $db;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $token = sanitize_input($input['token'] ?? '');
        $password = $input['password'] ?? '';
        $confirm_password = $input['confirm_password'] ?? '';
        
        if (empty($token) || empty($password) || empty($confirm_password)) {
            throw new Exception('All fields are required');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Password must be at least 6 characters long');
        }
        
        if ($password !== $confirm_password) {
            throw new Exception('Passwords do not match');
        }
        
        // Verify token
        $query = "SELECT email FROM password_resets WHERE token = ? AND expires_at > NOW()";
        $stmt = $db->prepare($query);
        $stmt->execute([$token]);
        $reset = $stmt->fetch();
        
        if (!$reset) {
            throw new Exception('Invalid or expired reset token');
        }
        
        // Update password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $query = "UPDATE users SET password = ? WHERE email = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$hashed_password, $reset['email']]);
        
        // Delete reset token
        $query = "DELETE FROM password_resets WHERE token = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$token]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Password has been reset successfully. You can now login.'
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

function sendVerificationEmail($email, $token) {
    // Implement email sending logic
    // For now, just log the token (in production, use proper email service)
    error_log("Verification token for $email: $token");
}

function sendPasswordResetEmail($email, $token, $name) {
    // Implement email sending logic
    $reset_link = SITE_URL . "/reset-password.php?token=" . $token;
    error_log("Password reset link for $email: $reset_link");
}
?>
