// Main JavaScript file for E-Commerce site

// Global variables
let cartDropdownTimeout;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize image lazy loading
    initializeLazyLoading();
    
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize cart dropdown
    initializeCartDropdown();
    
    // Initialize product quick view
    initializeQuickView();
    
    // Initialize search functionality
    initializeSearch();
}

// Tooltip initialization
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const element = event.target;
    const tooltipText = element.getAttribute('data-tooltip');
    
    const tooltip = document.createElement('div');
    tooltip.className = 'absolute bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg z-50';
    tooltip.textContent = tooltipText;
    tooltip.id = 'tooltip';
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

function hideTooltip() {
    const tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Lazy loading for images
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', validateForm);
    });
}

function validateForm(event) {
    const form = event.target;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    if (!isValid) {
        event.preventDefault();
    }
}

function validateInput(input) {
    const value = input.value.trim();
    const type = input.type;
    let isValid = true;
    let errorMessage = '';
    
    // Required validation
    if (input.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }
    
    // Email validation
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address';
        }
    }
    
    // Password validation
    if (type === 'password' && value && value.length < 6) {
        isValid = false;
        errorMessage = 'Password must be at least 6 characters long';
    }
    
    // Phone validation
    if (input.name === 'phone' && value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/\s/g, ''))) {
            isValid = false;
            errorMessage = 'Please enter a valid phone number';
        }
    }
    
    // Show/hide error message
    showInputError(input, isValid ? '' : errorMessage);
    
    return isValid;
}

function showInputError(input, message) {
    // Remove existing error
    const existingError = input.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    if (message) {
        input.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
    } else {
        input.classList.remove('error');
    }
}

// Cart dropdown functionality
function initializeCartDropdown() {
    const cartButton = document.querySelector('[data-cart-toggle]');
    const cartDropdown = document.querySelector('[data-cart-dropdown]');
    
    if (cartButton && cartDropdown) {
        cartButton.addEventListener('mouseenter', () => {
            clearTimeout(cartDropdownTimeout);
            showCartDropdown();
        });
        
        cartButton.addEventListener('mouseleave', () => {
            cartDropdownTimeout = setTimeout(hideCartDropdown, 300);
        });
        
        cartDropdown.addEventListener('mouseenter', () => {
            clearTimeout(cartDropdownTimeout);
        });
        
        cartDropdown.addEventListener('mouseleave', () => {
            cartDropdownTimeout = setTimeout(hideCartDropdown, 300);
        });
    }
}

function showCartDropdown() {
    const dropdown = document.querySelector('[data-cart-dropdown]');
    if (dropdown) {
        dropdown.classList.add('show');
        loadCartItems();
    }
}

function hideCartDropdown() {
    const dropdown = document.querySelector('[data-cart-dropdown]');
    if (dropdown) {
        dropdown.classList.remove('show');
    }
}

function loadCartItems() {
    fetch('api/cart.php?action=items')
        .then(response => response.json())
        .then(data => {
            const cartItems = document.getElementById('cart-items');
            if (cartItems && data.items) {
                cartItems.innerHTML = data.items.map(item => `
                    <div class="flex items-center p-3 border-b">
                        <img src="${item.image}" alt="${item.name}" class="w-12 h-12 object-cover rounded">
                        <div class="ml-3 flex-1">
                            <h4 class="text-sm font-medium">${item.name}</h4>
                            <p class="text-xs text-gray-500">${item.quantity} x $${item.price}</p>
                        </div>
                        <button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');
            }
        });
}

// Quick view functionality
function initializeQuickView() {
    const quickViewButtons = document.querySelectorAll('[data-quick-view]');
    quickViewButtons.forEach(button => {
        button.addEventListener('click', openQuickView);
    });
}

function openQuickView(event) {
    const productId = event.target.getAttribute('data-product-id');
    
    fetch(`api/products.php?action=quick_view&id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showQuickViewModal(data.product);
            }
        });
}

function showQuickViewModal(product) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold">${product.name}</h2>
                    <button onclick="closeQuickView()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <img src="${product.image}" alt="${product.name}" class="w-full rounded-lg">
                    </div>
                    <div>
                        <p class="text-gray-600 mb-4">${product.description}</p>
                        <div class="text-2xl font-bold text-green-600 mb-4">$${product.price}</div>
                        <button onclick="addToCart(${product.id})" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function closeQuickView() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
        modal.remove();
        document.body.style.overflow = '';
    }
}

// Search functionality
function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3) {
                    showSearchSuggestions(this.value);
                } else {
                    hideSearchSuggestions();
                }
            }, 300);
        });
    }
}

function showSearchSuggestions(query) {
    fetch(`api/search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            const suggestions = document.getElementById('search-suggestions');
            if (suggestions && data.suggestions) {
                suggestions.innerHTML = data.suggestions.map(item => `
                    <a href="?page=product&id=${item.id}" class="block p-2 hover:bg-gray-100">
                        <div class="flex items-center">
                            <img src="${item.image}" alt="${item.name}" class="w-8 h-8 object-cover rounded mr-3">
                            <span>${item.name}</span>
                        </div>
                    </a>
                `).join('');
                suggestions.classList.remove('hidden');
            }
        });
}

function hideSearchSuggestions() {
    const suggestions = document.getElementById('search-suggestions');
    if (suggestions) {
        suggestions.classList.add('hidden');
    }
}

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateCartCount = updateCartCount;
window.showNotification = showNotification;
window.closeQuickView = closeQuickView;
