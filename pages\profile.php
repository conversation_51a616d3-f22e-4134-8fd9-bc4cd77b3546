<?php
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ?page=login');
    exit;
}

// Get user data
$query = "SELECT * FROM users WHERE id = ?";
$stmt = $db->prepare($query);
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    session_destroy();
    header('Location: ?page=login');
    exit;
}

// Get user addresses
$query = "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, type";
$stmt = $db->prepare($query);
$stmt->execute([$_SESSION['user_id']]);
$addresses = $stmt->fetchAll();

// Get recent orders
$query = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute([$_SESSION['user_id']]);
$recent_orders = $stmt->fetchAll();
?>

<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="breadcrumb">
        <span class="breadcrumb-item"><a href="index.php">Home</a></span>
        <span class="breadcrumb-item active">My Account</span>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-2xl text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                    <p class="text-gray-600"><?php echo htmlspecialchars($user['email']); ?></p>
                </div>
                
                <nav class="space-y-2">
                    <a href="#profile-info" class="profile-tab block w-full text-left px-4 py-2 rounded-md bg-primary text-white">
                        <i class="fas fa-user mr-2"></i>Profile Information
                    </a>
                    <a href="#addresses" class="profile-tab block w-full text-left px-4 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-map-marker-alt mr-2"></i>Addresses
                    </a>
                    <a href="#orders" class="profile-tab block w-full text-left px-4 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-shopping-bag mr-2"></i>Order History
                    </a>
                    <a href="#security" class="profile-tab block w-full text-left px-4 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-lock mr-2"></i>Security
                    </a>
                    <?php if ($site_settings['features']['wishlist']): ?>
                    <a href="?page=wishlist" class="block w-full text-left px-4 py-2 rounded-md text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-heart mr-2"></i>Wishlist
                    </a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Profile Information -->
            <div id="profile-info" class="profile-content bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-bold mb-6">Profile Information</h2>
                
                <form id="profile-form" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                                First Name
                            </label>
                            <input type="text" id="first_name" name="first_name" 
                                   value="<?php echo htmlspecialchars($user['first_name']); ?>" required
                                   class="form-control">
                        </div>
                        
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Last Name
                            </label>
                            <input type="text" id="last_name" name="last_name" 
                                   value="<?php echo htmlspecialchars($user['last_name']); ?>" required
                                   class="form-control">
                        </div>
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo htmlspecialchars($user['email']); ?>" required
                               class="form-control">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($user['phone']); ?>"
                               class="form-control">
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark btn-animate">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Addresses -->
            <div id="addresses" class="profile-content bg-white rounded-lg shadow-md p-6 hidden">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">My Addresses</h2>
                    <button id="add-address-btn" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark">
                        <i class="fas fa-plus mr-2"></i>Add Address
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php foreach ($addresses as $address): ?>
                    <div class="border rounded-lg p-4 relative">
                        <?php if ($address['is_default']): ?>
                        <span class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                            Default
                        </span>
                        <?php endif; ?>
                        
                        <div class="mb-2">
                            <span class="inline-block bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded uppercase">
                                <?php echo htmlspecialchars($address['type']); ?>
                            </span>
                        </div>
                        
                        <div class="text-sm">
                            <p class="font-medium"><?php echo htmlspecialchars($address['first_name'] . ' ' . $address['last_name']); ?></p>
                            <?php if ($address['company']): ?>
                            <p><?php echo htmlspecialchars($address['company']); ?></p>
                            <?php endif; ?>
                            <p><?php echo htmlspecialchars($address['address_line_1']); ?></p>
                            <?php if ($address['address_line_2']): ?>
                            <p><?php echo htmlspecialchars($address['address_line_2']); ?></p>
                            <?php endif; ?>
                            <p><?php echo htmlspecialchars($address['city'] . ', ' . $address['state'] . ' ' . $address['postal_code']); ?></p>
                            <p><?php echo htmlspecialchars($address['country']); ?></p>
                            <?php if ($address['phone']): ?>
                            <p><?php echo htmlspecialchars($address['phone']); ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mt-4 flex space-x-2">
                            <button class="edit-address text-blue-600 hover:text-blue-800 text-sm" 
                                    data-address-id="<?php echo $address['id']; ?>">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button class="delete-address text-red-600 hover:text-red-800 text-sm" 
                                    data-address-id="<?php echo $address['id']; ?>">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Order History -->
            <div id="orders" class="profile-content bg-white rounded-lg shadow-md p-6 hidden">
                <h2 class="text-2xl font-bold mb-6">Order History</h2>
                
                <?php if (empty($recent_orders)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-shopping-bag text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600">You haven't placed any orders yet.</p>
                    <a href="?page=products" class="inline-block mt-4 bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark">
                        Start Shopping
                    </a>
                </div>
                <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($recent_orders as $order): ?>
                    <div class="border rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <h3 class="font-semibold">Order #<?php echo htmlspecialchars($order['order_number']); ?></h3>
                                <p class="text-sm text-gray-600">
                                    Placed on <?php echo date('M j, Y', strtotime($order['created_at'])); ?>
                                </p>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-<?php echo $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'cancelled' ? 'danger' : 'info'); ?>">
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                                <p class="text-lg font-semibold mt-1">$<?php echo number_format($order['total_amount'], 2); ?></p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-gray-600">
                                Payment: <?php echo ucfirst($order['payment_status']); ?>
                            </p>
                            <a href="?page=order&id=<?php echo $order['id']; ?>" 
                               class="text-primary hover:text-primary-dark text-sm">
                                View Details <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="text-center mt-6">
                    <a href="?page=orders" class="text-primary hover:text-primary-dark">
                        View All Orders <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Security -->
            <div id="security" class="profile-content bg-white rounded-lg shadow-md p-6 hidden">
                <h2 class="text-2xl font-bold mb-6">Security Settings</h2>
                
                <form id="password-form" class="space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                            Current Password
                        </label>
                        <input type="password" id="current_password" name="current_password" required
                               class="form-control">
                    </div>
                    
                    <div>
                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                            New Password
                        </label>
                        <input type="password" id="new_password" name="new_password" required
                               class="form-control">
                    </div>
                    
                    <div>
                        <label for="confirm_new_password" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm New Password
                        </label>
                        <input type="password" id="confirm_new_password" name="confirm_new_password" required
                               class="form-control">
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark btn-animate">
                            Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabs = document.querySelectorAll('.profile-tab');
    const contents = document.querySelectorAll('.profile-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabs.forEach(t => {
                t.classList.remove('bg-primary', 'text-white');
                t.classList.add('text-gray-700', 'hover:bg-gray-100');
            });
            
            // Add active class to clicked tab
            this.classList.add('bg-primary', 'text-white');
            this.classList.remove('text-gray-700', 'hover:bg-gray-100');
            
            // Hide all content
            contents.forEach(content => content.classList.add('hidden'));
            
            // Show target content
            const target = this.getAttribute('href').substring(1);
            document.getElementById(target).classList.remove('hidden');
        });
    });
    
    // Profile form submission
    document.getElementById('profile-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            action: 'update_profile',
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            csrf_token: formData.get('csrf_token')
        };
        
        fetch('api/profile.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            showNotification(data.message, data.success ? 'success' : 'error');
        })
        .catch(error => {
            showNotification('An error occurred. Please try again.', 'error');
        });
    });
    
    // Password form submission
    document.getElementById('password-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        if (formData.get('new_password') !== formData.get('confirm_new_password')) {
            showNotification('New passwords do not match', 'error');
            return;
        }
        
        const data = {
            action: 'change_password',
            current_password: formData.get('current_password'),
            new_password: formData.get('new_password'),
            csrf_token: formData.get('csrf_token')
        };
        
        fetch('api/profile.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            showNotification(data.message, data.success ? 'success' : 'error');
            if (data.success) {
                this.reset();
            }
        })
        .catch(error => {
            showNotification('An error occurred. Please try again.', 'error');
        });
    });
});
</script>
