<?php
require_once 'config/database.php';

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Application is already installed. Delete config/installed.lock to reinstall.');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Create database connection
        $database = new Database();
        
        // Create database if it doesn't exist
        if ($database->createDatabase()) {
            $db = $database->getConnection();
            
            // Read and execute SQL schema
            $sql = file_get_contents('database/schema.sql');
            
            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                    $db->exec($statement);
                }
            }
            
            // Create admin user
            $admin_username = $_POST['admin_username'] ?? 'admin';
            $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
            $admin_password = password_hash($_POST['admin_password'] ?? 'admin123', PASSWORD_DEFAULT);
            
            $query = "INSERT INTO users (username, email, password, first_name, last_name, is_admin, email_verified) 
                     VALUES (?, ?, ?, 'Admin', 'User', 1, 1)";
            $stmt = $db->prepare($query);
            $stmt->execute([$admin_username, $admin_email, $admin_password]);
            
            // Create uploads directory
            if (!file_exists('uploads')) {
                mkdir('uploads', 0755, true);
                mkdir('uploads/products', 0755, true);
                mkdir('uploads/categories', 0755, true);
                mkdir('uploads/users', 0755, true);
            }
            
            // Create installation lock file
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            
            $message = 'Installation completed successfully! You can now <a href="index.php">visit your store</a> or <a href="admin/">login to admin panel</a>.';
            
        } else {
            $error = 'Failed to create database.';
        }
        
    } catch (Exception $e) {
        $error = 'Installation failed: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Commerce Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-center mb-6">E-Commerce Installation</h1>
            
            <?php if ($message): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!$message): ?>
            <form method="POST" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Admin Username</label>
                    <input type="text" name="admin_username" value="admin" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Admin Email</label>
                    <input type="email" name="admin_email" value="<EMAIL>" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700">Admin Password</label>
                    <input type="password" name="admin_password" value="admin123" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <button type="submit" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Install E-Commerce
                </button>
            </form>
            <?php endif; ?>
            
            <div class="mt-6 text-sm text-gray-600">
                <h3 class="font-medium">Requirements:</h3>
                <ul class="list-disc list-inside mt-2">
                    <li>PHP 7.4 or higher</li>
                    <li>MySQL 5.7 or higher</li>
                    <li>Apache/Nginx web server</li>
                    <li>Write permissions for uploads directory</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
