<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Site configuration
define('SITE_URL', 'http://localhost/E-Commerce');
define('SITE_NAME', 'E-Commerce Store');
define('SITE_EMAIL', '<EMAIL>');

// Database configuration
require_once 'database.php';

// Security settings
define('ENCRYPTION_KEY', 'your-secret-key-here-change-in-production');
define('JWT_SECRET', 'your-jwt-secret-key-here');

// Upload settings
define('UPLOAD_PATH', '../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Email settings (for SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');

// Payment gateway settings
define('RAZORPAY_KEY_ID', 'your-razorpay-key-id');
define('RAZORPAY_KEY_SECRET', 'your-razorpay-key-secret');

// Pagination settings
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);

// Security functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Auto-load classes
spl_autoload_register(function ($class_name) {
    $directories = [
        '../classes/',
        '../models/',
        '../controllers/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            break;
        }
    }
});

// Initialize database
$database = new Database();
$db = $database->getConnection();

// Load site settings
function loadSiteSettings() {
    global $db;
    try {
        $query = "SELECT * FROM site_settings WHERE id = 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $settings = $stmt->fetch();
        
        if ($settings) {
            return json_decode($settings['settings_json'], true);
        }
    } catch (Exception $e) {
        // Return default settings if table doesn't exist yet
    }
    
    return [
        'site_name' => SITE_NAME,
        'theme' => 'default',
        'features' => [
            'reviews' => true,
            'wishlist' => true,
            'coupons' => true,
            'newsletter' => true,
            'chatbot' => false,
            'dark_mode' => true,
            'social_login' => false
        ]
    ];
}

$site_settings = loadSiteSettings();
?>
