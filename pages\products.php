<?php
// Get filter parameters
$category_slug = sanitize_input($_GET['category'] ?? '');
$search = sanitize_input($_GET['search'] ?? '');
$sort = sanitize_input($_GET['sort'] ?? 'name');
$order = sanitize_input($_GET['order'] ?? 'ASC');
$page = max(1, intval($_GET['page'] ?? 1));
$min_price = floatval($_GET['min_price'] ?? 0);
$max_price = floatval($_GET['max_price'] ?? 0);

// Get current category info
$current_category = null;
if ($category_slug) {
    $query = "SELECT * FROM categories WHERE slug = ? AND is_active = 1";
    $stmt = $db->prepare($query);
    $stmt->execute([$category_slug]);
    $current_category = $stmt->fetch();
}

// Get all categories for filter
$query = "SELECT * FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

// Build products query
$where_conditions = ["p.status = 'active'"];
$params = [];

if ($category_slug) {
    $where_conditions[] = "c.slug = ?";
    $params[] = $category_slug;
}

if ($search) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($min_price > 0) {
    $where_conditions[] = "p.price >= ?";
    $params[] = $min_price;
}

if ($max_price > 0) {
    $where_conditions[] = "p.price <= ?";
    $params[] = $max_price;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_query = "SELECT COUNT(*) as total 
               FROM products p 
               LEFT JOIN categories c ON p.category_id = c.id 
               WHERE $where_clause";
$stmt = $db->prepare($count_query);
$stmt->execute($params);
$total_products = $stmt->fetch()['total'];

// Pagination
$limit = PRODUCTS_PER_PAGE;
$offset = ($page - 1) * $limit;
$total_pages = ceil($total_products / $limit);

// Validate sort
$allowed_sorts = ['name', 'price', 'created_at'];
if (!in_array($sort, $allowed_sorts)) {
    $sort = 'name';
}
$order = strtoupper($order) === 'DESC' ? 'DESC' : 'ASC';

// Get products
$query = "SELECT p.*, c.name as category_name, c.slug as category_slug,
                 (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                 (SELECT AVG(rating) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
                 (SELECT COUNT(*) FROM product_reviews WHERE product_id = p.id AND is_approved = 1) as review_count
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          WHERE $where_clause 
          ORDER BY p.$sort $order 
          LIMIT $limit OFFSET $offset";

$stmt = $db->prepare($query);
$stmt->execute($params);
$products = $stmt->fetchAll();

// Get price range for filters
$price_query = "SELECT MIN(price) as min_price, MAX(price) as max_price FROM products WHERE status = 'active'";
$stmt = $db->prepare($price_query);
$stmt->execute();
$price_range = $stmt->fetch();
?>

<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="breadcrumb">
        <span class="breadcrumb-item"><a href="index.php">Home</a></span>
        <?php if ($current_category): ?>
        <span class="breadcrumb-item"><a href="?page=products">Products</a></span>
        <span class="breadcrumb-item active"><?php echo htmlspecialchars($current_category['name']); ?></span>
        <?php else: ?>
        <span class="breadcrumb-item active">Products</span>
        <?php endif; ?>
    </nav>

    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold mb-2">
            <?php if ($current_category): ?>
                <?php echo htmlspecialchars($current_category['name']); ?>
            <?php elseif ($search): ?>
                Search Results for "<?php echo htmlspecialchars($search); ?>"
            <?php else: ?>
                All Products
            <?php endif; ?>
        </h1>
        <p class="text-gray-600">
            <?php echo $total_products; ?> product<?php echo $total_products !== 1 ? 's' : ''; ?> found
        </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
                <h3 class="text-lg font-semibold mb-4">Filters</h3>
                
                <form id="filter-form" method="GET">
                    <input type="hidden" name="page" value="products">
                    <?php if ($search): ?>
                    <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                    <?php endif; ?>
                    
                    <!-- Categories -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Categories</h4>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="category" value="" 
                                       <?php echo !$category_slug ? 'checked' : ''; ?>
                                       class="mr-2">
                                <span>All Categories</span>
                            </label>
                            <?php foreach ($categories as $category): ?>
                            <label class="flex items-center">
                                <input type="radio" name="category" value="<?php echo $category['slug']; ?>" 
                                       <?php echo $category_slug === $category['slug'] ? 'checked' : ''; ?>
                                       class="mr-2">
                                <span><?php echo htmlspecialchars($category['name']); ?></span>
                            </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="mb-6">
                        <h4 class="font-medium mb-3">Price Range</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Min Price</label>
                                <input type="number" name="min_price" value="<?php echo $min_price; ?>" 
                                       min="0" max="<?php echo $price_range['max_price']; ?>" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-primary focus:border-primary">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">Max Price</label>
                                <input type="number" name="max_price" value="<?php echo $max_price; ?>" 
                                       min="0" max="<?php echo $price_range['max_price']; ?>" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-primary focus:border-primary">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="w-full bg-primary text-white py-2 rounded hover:bg-primary-dark transition-colors">
                        Apply Filters
                    </button>
                    
                    <?php if ($category_slug || $min_price || $max_price): ?>
                    <a href="?page=products<?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                       class="block w-full text-center mt-2 text-gray-600 hover:text-primary">
                        Clear Filters
                    </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="lg:col-span-3">
            <!-- Sort Options -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 bg-white rounded-lg shadow-md p-4">
                <div class="mb-4 sm:mb-0">
                    <span class="text-gray-600">
                        Showing <?php echo min($total_products, ($page - 1) * $limit + 1); ?>-<?php echo min($total_products, $page * $limit); ?> 
                        of <?php echo $total_products; ?> products
                    </span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <label class="text-sm text-gray-600">Sort by:</label>
                    <select id="sort-select" class="border border-gray-300 rounded px-3 py-1 focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="name-ASC" <?php echo ($sort === 'name' && $order === 'ASC') ? 'selected' : ''; ?>>Name (A-Z)</option>
                        <option value="name-DESC" <?php echo ($sort === 'name' && $order === 'DESC') ? 'selected' : ''; ?>>Name (Z-A)</option>
                        <option value="price-ASC" <?php echo ($sort === 'price' && $order === 'ASC') ? 'selected' : ''; ?>>Price (Low-High)</option>
                        <option value="price-DESC" <?php echo ($sort === 'price' && $order === 'DESC') ? 'selected' : ''; ?>>Price (High-Low)</option>
                        <option value="created_at-DESC" <?php echo ($sort === 'created_at' && $order === 'DESC') ? 'selected' : ''; ?>>Newest First</option>
                        <option value="created_at-ASC" <?php echo ($sort === 'created_at' && $order === 'ASC') ? 'selected' : ''; ?>>Oldest First</option>
                    </select>
                </div>
            </div>

            <!-- Products -->
            <?php if (empty($products)): ?>
            <div class="text-center py-12 bg-white rounded-lg shadow-md">
                <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No products found</h3>
                <p class="text-gray-500 mb-4">Try adjusting your search criteria or browse our categories.</p>
                <a href="?page=products" class="bg-primary text-white px-6 py-2 rounded hover:bg-primary-dark transition-colors">
                    View All Products
                </a>
            </div>
            <?php else: ?>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <?php foreach ($products as $product): ?>
                <div class="product-card bg-white rounded-lg shadow-md overflow-hidden group">
                    <div class="relative image-zoom">
                        <img src="<?php echo $product['primary_image'] ?: 'assets/images/no-image.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             class="w-full h-48 object-cover">
                        
                        <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                        <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
                            <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>% OFF
                        </span>
                        <?php endif; ?>
                        
                        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <?php if ($site_settings['features']['wishlist']): ?>
                            <button class="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 mb-2" 
                                    onclick="addToWishlist(<?php echo $product['id']; ?>)"
                                    data-tooltip="Add to Wishlist">
                                <i class="fas fa-heart text-gray-600"></i>
                            </button>
                            <?php endif; ?>
                            <button class="bg-white p-2 rounded-full shadow-md hover:bg-gray-100" 
                                    onclick="openQuickView(event)" 
                                    data-product-id="<?php echo $product['id']; ?>"
                                    data-tooltip="Quick View">
                                <i class="fas fa-eye text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="p-4">
                        <div class="text-sm text-gray-500 mb-1"><?php echo htmlspecialchars($product['category_name']); ?></div>
                        <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2">
                            <a href="?page=product&slug=<?php echo $product['slug']; ?>" class="hover:text-primary">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </a>
                        </h3>
                        
                        <?php if ($product['avg_rating'] > 0): ?>
                        <div class="flex items-center mb-2">
                            <div class="star-rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star star <?php echo $i <= $product['avg_rating'] ? 'filled' : ''; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="text-sm text-gray-500 ml-2">(<?php echo $product['review_count']; ?>)</span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="flex items-center justify-between">
                            <div class="price-container">
                                <?php if ($product['sale_price'] && $product['sale_price'] < $product['price']): ?>
                                <span class="price text-lg">$<?php echo number_format($product['sale_price'], 2); ?></span>
                                <span class="price-old text-sm ml-2">$<?php echo number_format($product['price'], 2); ?></span>
                                <?php else: ?>
                                <span class="price text-lg">$<?php echo number_format($product['price'], 2); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <button onclick="addToCart(<?php echo $product['id']; ?>)" 
                                    class="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition-colors btn-animate">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="flex justify-center">
                <nav class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" 
                       class="px-3 py-2 border border-gray-300 rounded hover:bg-gray-100">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <?php endif; ?>
                    
                    <?php
                    $start = max(1, $page - 2);
                    $end = min($total_pages, $page + 2);
                    
                    for ($i = $start; $i <= $end; $i++):
                    ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                       class="px-3 py-2 border border-gray-300 rounded <?php echo $i === $page ? 'bg-primary text-white' : 'hover:bg-gray-100'; ?>">
                        <?php echo $i; ?>
                    </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" 
                       class="px-3 py-2 border border-gray-300 rounded hover:bg-gray-100">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sort functionality
    document.getElementById('sort-select').addEventListener('change', function() {
        const [sort, order] = this.value.split('-');
        const url = new URL(window.location);
        url.searchParams.set('sort', sort);
        url.searchParams.set('order', order);
        url.searchParams.set('page', '1'); // Reset to first page
        window.location.href = url.toString();
    });
});

// Wishlist functionality
function addToWishlist(productId) {
    <?php if (isset($_SESSION['user_id'])): ?>
    fetch('api/wishlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '<?php echo generate_csrf_token(); ?>'
        },
        body: JSON.stringify({
            action: 'add',
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Product added to wishlist!', 'success');
        } else {
            showNotification(data.message || 'Error adding to wishlist', 'error');
        }
    })
    .catch(error => {
        showNotification('Error adding to wishlist', 'error');
    });
    <?php else: ?>
    showNotification('Please login to add items to wishlist', 'error');
    setTimeout(() => {
        window.location.href = '?page=login';
    }, 2000);
    <?php endif; ?>
}
</script>
